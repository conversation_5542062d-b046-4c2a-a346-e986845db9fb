<script setup lang="tsx">
import { N<PERSON><PERSON>on, NPopconfirm } from 'naive-ui';
import type { DataTableSortState } from 'naive-ui';
import { skuApi } from '@/service/api/wms';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import dayjs from 'dayjs';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';
import emitter from '@/utils/mitt';

const props = defineProps<{
  item: Api.Wms.Item | null;
}>();

const appStore = useAppStore();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  searchParams,
  resetSearchParams,
  updateSearchParams
} = useTable({
  apiFn: skuApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: null,
    name: null,
    code: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'name',
      title: '规格名',
      align: 'left',
      minWidth: 300,
      ellipsis: true
    },
    {
      key: 'code',
      title: '编码',
      align: 'left'
    },
    {
      key: 'unit',
      title: '单位',
      align: 'left'
    },
    {
      key: 'min',
      title: '预警库存',
      align: 'left'
    },
    {
      key: 'order',
      title: '排序',
      align: 'left',
      sorter: true
    },
    {
      key: 'createdAt',
      title: '创建时间',
      align: 'left',
      sorter: true,
      render: row => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm')
    },
    {
      key: 'createdBy',
      title: '创建人',
      align: 'left'
    },
    {
      key: 'updatedBy',
      title: '更新人',
      align: 'left'
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => <SwitchStatus v-model:value={row.status} apiFn={status => skuApi.save({ ...row, status })} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await skuApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await skuApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'order,id',
      _order: 'desc,asc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div class="h-full flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        >
          <template #suffix>
            <NButton @click="emitter.emit('showSku', null)">返回</NButton>
          </template>
        </TableHeaderOperation>
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
        @update:sorter="handleSort"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
