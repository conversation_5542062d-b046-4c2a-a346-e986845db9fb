<script setup lang="ts">
import { onUnmounted, ref } from 'vue';
import Item from './modules/item/index.vue';
import Sku from './modules/sku/index.vue';
import emitter from '@/utils/mitt';

const item = ref<Api.Wms.Item | null>(null);

emitter.on('showSku', (data: Api.Wms.Item | null) => {
  item.value = data;
});

onUnmounted(() => {
  emitter.off('showSku');
});
</script>

<template>
  <div class="h-full">
    <Item v-show="!item" />
    <Sku v-show="item" :item="item" />
  </div>
</template>

<style scoped></style>
